import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'page_home.dart';
import 'conducteur_home_page.dart';

class PasswordPage extends StatefulWidget {
  final String userId;
  final bool isConducteur;

  const PasswordPage({super.key, required this.userId, this.isConducteur = false});

  @override
  _PasswordPageState createState() => _PasswordPageState();
}

class _PasswordPageState extends State<PasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  bool isLoading = false;

  Future<void> setPassword() async {
    if (!_formKey.currentState!.validate()) {
      print("Validation du formulaire échouée");
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final Map<String, dynamic> data = {
        'userId': widget.userId,
        'password': passwordController.text.trim(),
        'confirmPassword': confirmPasswordController.text.trim(),
      };

      print("Données envoyées : ${jsonEncode(data)}");

      final response = await http.post(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/set-password'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(data),
      );

      print("Statut de la réponse : ${response.statusCode}");
      print("Corps de la réponse : ${response.body}");

      if (response.statusCode == 200) {
        print("Enregistrement du mot de passe réussi, redirection vers ${widget.isConducteur ? 'ConducteurHomePage' : 'PageHome'}");
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (_) => widget.isConducteur
                ? ConducteurHomePage(userId: widget.userId)
                : PageHome(userId: widget.userId),
          ),
        );
        passwordController.clear();
        confirmPasswordController.clear();
      } else {
        final errorMessage = jsonDecode(response.body)['message'] ?? "Erreur inconnue";
        print("Erreur lors de l'enregistrement : $errorMessage");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: $errorMessage")),
        );
      }
    } catch (e) {
      print("Exception capturée : $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Erreur de connexion au serveur : $e")),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Définir le mot de passe")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: passwordController,
                decoration: const InputDecoration(labelText: "Mot de passe *"),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Mot de passe obligatoire";
                  }
                  if (value.length < 6) {
                    return "Le mot de passe doit contenir au moins 6 caractères";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: confirmPasswordController,
                decoration: const InputDecoration(labelText: "Confirmer mot de passe *"),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Confirmation obligatoire";
                  }
                  if (value != passwordController.text) {
                    return "Les mots de passe ne correspondent pas";
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: setPassword,
                      child: const Text("Créer le compte"),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}