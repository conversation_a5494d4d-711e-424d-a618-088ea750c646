import 'package:flutter/material.dart';

class PageHome extends StatefulWidget {
  final String userId;

  const PageHome({Key? key, required this.userId}) : super(key: key);

  @override
  _PageHomeState createState() => _PageHomeState();
}

class _PageHomeState extends State<PageHome> {
  int _selectedIndex = 0;
  final TextEditingController searchController = TextEditingController();

  final List<String> menuItems = ["Home", "Notifications", "Messages", "Compte"];

  late final List<Widget> pages;

  @override
  void initState() {
    super.initState();
    // Initialisation des pages avec userId pour la page "Compte"
    pages = [
      Center(child: Text("🏠 Contenu Home", style: TextStyle(fontSize: 20))),
      Center(child: Text("🔔 Contenu Notifications", style: TextStyle(fontSize: 20))),
      Center(child: Text("💬 Contenu Messages", style: TextStyle(fontSize: 20))),
      Center(child: Text("👤 Contenu Compte (ID: ${widget.userId})", style: TextStyle(fontSize: 20))),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: searchController,
          decoration: InputDecoration(
            hintText: "Rechercher...",
            border: InputBorder.none,
          ),
          style: TextStyle(color: Colors.white),
          cursorColor: Colors.white,
          onSubmitted: (value) {
            print("Recherche: $value (User ID: ${widget.userId})");
          },
        ),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          // Menu horizontal
          Container(
            color: Colors.blue,
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final isSelected = _selectedIndex == index;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    decoration: isSelected
                        ? BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Colors.white, width: 3),
                            ),
                          )
                        : null,
                    child: Text(
                      menuItems[index],
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Contenu de la page
          Expanded(
            child: pages[_selectedIndex],
          ),
        ],
      ),
    );
  }
}