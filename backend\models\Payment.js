const PaymentSchema = new mongoose.Schema({
  rideId: { type: mongoose.Schema.Types.ObjectId, ref: 'Ride', required: true },
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  amount: { type: Number, required: true },               // المبلغ الإجمالي المدفوع
  method: { type: String, enum: ['card', 'cash'], required: true },
  status: { type: String, enum: ['pending', 'paid', 'failed'], default: 'pending' },
  transactionId: { type: String },
  paidAt: { type: Date },

  commissionPercentage: { type: Number, default: 5 },     // نسبة العمولة، هنا 5%
  commissionAmount: { type: Number },                      // تحسب في الباكيند: amount * commissionPercentage / 100
  amountAfterCommission: { type: Number }                  // amount - commissionAmount
}, { timestamps: true });
