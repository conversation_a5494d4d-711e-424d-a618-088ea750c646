import 'package:flutter/material.dart';
import 'client_signup_page.dart';
import 'conducteur_signup_page.dart';

class RoleSelectionPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Créer un compte")),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: <PERSON><PERSON><PERSON>(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Choisissez votre type de compte",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 30),

            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => ClientSignupPage()),
                );
              },
              child: Text("Créer un compte Client"),
              style: ElevatedButton.styleFrom(minimumSize: Size(double.infinity, 50)),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),

            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => ConducteurSignupPage()),
                );
              },
              child: Text("Créer un compte Conducteur"),
              style: ElevatedButton.styleFrom(minimumSize: Size(double.infinity, 50)),
            ),
          ],
        ),
      ),
    );
  }
}
