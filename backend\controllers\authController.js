const bcrypt = require('bcrypt'); // Pour vérifier le mot de passe haché
const jwt = require('jsonwebtoken'); // <PERSON>ur générer un token JWT
const User = require('../models/User'); // Importer le modèle User

// Fonction de connexion
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Vérifier si l'e-mail existe dans la table User
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ message: 'Adresse e-mail ou mot de passe incorrect.' });
    }

    // Vérifier si le mot de passe correspond
    const isMatch = await bcrypt.compare(password, user.passwordHash);
    if (!isMatch) {
      return res.status(401).json({ message: 'Adresse e-mail ou mot de passe incorrect.' });
    }

    // Générer un token JWT pour l'authentification
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET, // Clé secrète définie dans .env
      { expiresIn: '1h' } // Durée de validité du token
    );

    // Réponse avec les informations de l'utilisateur et le token
    res.status(200).json({
      message: 'Connexion réussie',
      token,
      user: {
        id: user._id,
        role: user.role,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Erreur serveur.' });
  }
};