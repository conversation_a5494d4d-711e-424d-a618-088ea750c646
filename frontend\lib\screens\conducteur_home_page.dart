import 'package:flutter/material.dart';
import 'driver_profile_page.dart'; // Import de la nouvelle page de profil

class ConducteurHomePage extends StatefulWidget {
  final String userId;

  const ConducteurHomePage({Key? key, required this.userId}) : super(key: key);

  @override
  _ConducteurHomePageState createState() => _ConducteurHomePageState();
}

class _ConducteurHomePageState extends State<ConducteurHomePage> {
  int _selectedIndex = 0;
  final TextEditingController searchController = TextEditingController();

  final List<String> menuItems = ["Trajets", "Historique", "Profil"];

  late final List<Widget> pages;

  @override
  void initState() {
    super.initState();
    // Initialisation des pages
    pages = [
      Center(child: Text("🚗 Trajets disponibles", style: TextStyle(fontSize: 20))),
      Center(child: Text("📜 Historique des trajets", style: TextStyle(fontSize: 20))),
      // Navigation vers DriverProfilePage au lieu d'afficher directement le profil
      DriverProfilePage(userId: widget.userId),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: searchController,
          decoration: InputDecoration(
            hintText: "Rechercher un trajet...",
            border: InputBorder.none,
          ),
          style: TextStyle(color: Colors.white),
          cursorColor: Colors.white,
          onSubmitted: (value) {
            print("Recherche: $value (User ID: ${widget.userId})");
          },
        ),
        backgroundColor: Colors.green,
      ),
      body: Column(
        children: [
          // Menu horizontal
          Container(
            color: Colors.green,
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final isSelected = _selectedIndex == index;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    decoration: isSelected
                        ? BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Colors.white, width: 3),
                            ),
                          )
                        : null,
                    child: Text(
                      menuItems[index],
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          // Contenu de la page
          Expanded(
            child: pages[_selectedIndex],
          ),
        ],
      ),
    );
  }
}