import 'package:flutter/material.dart';
import 'roleselectionpage.dart';
import 'admin_home.dart';
import 'page_home.dart';
import 'conducteur_home_page.dart';
import '../services/api_service.dart';

class LoginPage extends StatelessWidget {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  // Fonction pour gérer la connexion
  Future<void> _login(BuildContext context) async {
    final String email = emailController.text.trim();
    final String password = passwordController.text.trim();

    if (email.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Veuillez entrer votre email et mot de passe.')),
      );
      return;
    }

    try {
      print("Tentative de connexion pour: $email");

      // Utiliser le service API pour la connexion
      final result = await ApiService.login(email, password);

      if (result.success) {
        // Redirection en fonction du rôle avec pushNamed
        Navigator.pushNamed(
          context,
          result.role == 'admin' ? '/admin' : result.role == 'conducteur' ? '/conducteur' : '/home',
          arguments: result.userId,
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.errorMessage ?? 'Erreur de connexion.')),
        );
      }
    } catch (e) {
      print("Exception lors de la connexion: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur de connexion au serveur. Vérifiez votre connexion internet.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Connexion")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: emailController,
              decoration: InputDecoration(
                labelText: "Adresse e-mail",
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: "Mot de passe",
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {
                  print("Mot de passe oublié");
                },
                child: Text("Mot de passe oublié ?"),
              ),
            ),
            ElevatedButton(
              onPressed: () => _login(context), // Appeler la fonction de connexion
              child: Text("Connexion"),
            ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => RoleSelectionPage()),
                );
              },
              child: Text("Créer un compte"),
            ),
          ],
        ),
      ),
    );
  }
}