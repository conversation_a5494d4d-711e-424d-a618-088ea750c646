import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/api_config.dart';

class ApiService {
  // Méthode pour faire un appel POST avec gestion d'erreur
  static Future<ApiResponse> post(String url, Map<String, dynamic> data) async {
    try {
      print("Envoi de données vers: $url");
      print("Données: ${jsonEncode(data)}");
      
      final response = await http.post(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(data),
      );
      
      print("Statut de la réponse: ${response.statusCode}");
      print("Corps de la réponse: ${response.body}");
      
      return ApiResponse(
        statusCode: response.statusCode,
        body: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    } catch (e) {
      print("Exception lors de l'appel API: $e");
      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: e.toString(),
      );
    }
  }
  
  // Méthode spécifique pour la connexion
  static Future<LoginResult> login(String email, String password) async {
    final response = await post(ApiConfig.loginUrl, {
      'email': email,
      'password': password,
    });
    
    if (response.isSuccess) {
      try {
        final data = jsonDecode(response.body);
        final user = data['user'];
        return LoginResult(
          success: true,
          userId: user['id'],
          role: user['role'],
        );
      } catch (e) {
        return LoginResult(
          success: false,
          errorMessage: 'Erreur de format de réponse du serveur.',
        );
      }
    } else {
      String errorMessage;
      try {
        final data = jsonDecode(response.body);
        errorMessage = data['message'] ?? ApiConfig.getErrorMessage(response.statusCode);
      } catch (e) {
        if (response.statusCode == 0) {
          errorMessage = ApiConfig.getConnectionErrorMessage(response.error);
        } else {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
      }
      
      return LoginResult(
        success: false,
        errorMessage: errorMessage,
      );
    }
  }
  
  // Méthode spécifique pour l'inscription
  static Future<RegisterResult> register(Map<String, dynamic> userData) async {
    final response = await post(ApiConfig.registerUrl, userData);
    
    if (response.isSuccess) {
      try {
        final data = jsonDecode(response.body);
        return RegisterResult(
          success: true,
          userId: data['userId'].toString(),
        );
      } catch (e) {
        return RegisterResult(
          success: false,
          errorMessage: 'Erreur de format de réponse du serveur.',
        );
      }
    } else {
      String errorMessage;
      try {
        final data = jsonDecode(response.body);
        errorMessage = data['message'] ?? ApiConfig.getErrorMessage(response.statusCode);
      } catch (e) {
        if (response.statusCode == 0) {
          errorMessage = ApiConfig.getConnectionErrorMessage(response.error);
        } else {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
      }
      
      return RegisterResult(
        success: false,
        errorMessage: errorMessage,
      );
    }
  }
}

// Classes pour les réponses
class ApiResponse {
  final int statusCode;
  final String body;
  final bool isSuccess;
  final String? error;
  
  ApiResponse({
    required this.statusCode,
    required this.body,
    required this.isSuccess,
    this.error,
  });
}

class LoginResult {
  final bool success;
  final String? userId;
  final String? role;
  final String? errorMessage;
  
  LoginResult({
    required this.success,
    this.userId,
    this.role,
    this.errorMessage,
  });
}

class RegisterResult {
  final bool success;
  final String? userId;
  final String? errorMessage;
  
  RegisterResult({
    required this.success,
    this.userId,
    this.errorMessage,
  });
}
