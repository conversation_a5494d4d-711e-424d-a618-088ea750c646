const RideSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  driverId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  vehicleId: { type: mongoose.Schema.Types.ObjectId, ref: 'Vehicle' },
  from: { cityId: mongoose.Schema.Types.ObjectId, address: String, coords: { type: [Number] } },
  to: { cityId: mongoose.Schema.Types.ObjectId, address: String, coords: { type: [Number] } },
  dateTime: Date,
  status: { type: String, enum: ['requested', 'accepted', 'on_ride', 'completed', 'cancelled', 'no_show'], default: 'requested' },
  fare: Number,
  paymentMethod: String,
  paymentStatus: { type: String, enum: ['pending', 'paid', 'failed'], default: 'pending' },
  baggage: { count: Number },
  notes: String
}, { timestamps: true });

RideSchema.index({ clientId: 1, dateTime: -1 });

module.exports = mongoose.model('Ride', RideSchema);
