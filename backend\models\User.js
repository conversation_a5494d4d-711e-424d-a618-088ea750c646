const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  role: { 
    type: String, 
    enum: ['client', 'conducteur', 'admin'], 
    required: true,
    default: 'client' 
  },
  nom: { type: String, required: true },
  prenom: { type: String, required: true },
  cin: { type: String, required: true, unique: true },
  age: { type: Number, required: true },
  email: { type: String, unique: true, sparse: true },
  phone: { type: String, unique: true, required: true },
  passwordHash: { type: String }, // Facultatif initialement
  ville: { type: String, required: true },
  photoUrl: { type: String },
  numero_permis: { 
    type: String, 
    required: function() { return this.role === 'conducteur'; },
    validate: {
      validator: function(v) {
        return this.role !== 'conducteur' || (v && v.length > 0);
      },
      message: 'Le numéro de permis est requis pour les conducteurs.'
    }
  },
  isVerified: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

UserSchema.index({ cin: 1 }, { unique: true });

module.exports = mongoose.model('User', UserSchema);