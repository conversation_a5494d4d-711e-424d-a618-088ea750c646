import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class DriverProfilePage extends StatefulWidget {
  final String userId;

  const DriverProfilePage({Key? key, required this.userId}) : super(key: key);

  @override
  _DriverProfilePageState createState() => _DriverProfilePageState();
}

class _DriverProfilePageState extends State<DriverProfilePage> {
  Map<String, dynamic>? userData;
  List<dynamic>? vehicles;
  bool isLoading = true;
  bool isEditing = false;
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nomController = TextEditingController();
  final TextEditingController prenomController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController villeController = TextEditingController();
  final TextEditingController numeroPermisController = TextEditingController();
  final TextEditingController brandController = TextEditingController();
  final TextEditingController modelController = TextEditingController();
  final TextEditingController yearController = TextEditingController();
  final TextEditingController plateNumberController = TextEditingController();
  final TextEditingController seatsController = TextEditingController();
  final TextEditingController allowBaggageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    fetchProfileData();
  }

  Future<void> fetchProfileData() async {
    setState(() {
      isLoading = true;
    });
    try {
      // Récupérer les données du conducteur
      final userResponse = await http.get(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );
      if (userResponse.statusCode == 200) {
        setState(() {
          userData = jsonDecode(userResponse.body);
          nomController.text = userData?['nom'] ?? '';
          prenomController.text = userData?['prenom'] ?? '';
          emailController.text = userData?['email'] ?? '';
          phoneController.text = userData?['phone'] ?? '';
          villeController.text = userData?['ville'] ?? '';
          numeroPermisController.text = userData?['numero_permis'] ?? '';
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la récupération du profil')),
        );
      }

      // Récupérer les voitures du conducteur
      final vehiclesResponse = await http.get(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/vehicles/driver/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );
      if (vehiclesResponse.statusCode == 200) {
        setState(() {
          vehicles = jsonDecode(vehiclesResponse.body);
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la récupération des voitures')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur serveur: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });
    try {
      final data = {
        'nom': nomController.text.trim(),
        'prenom': prenomController.text.trim(),
        'email': emailController.text.trim(),
        'phone': phoneController.text.trim(),
        'ville': villeController.text.trim(),
        'numero_permis': numeroPermisController.text.trim(),
      };
      final response = await http.put(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );
      if (response.statusCode == 200) {
        setState(() {
          isEditing = false;
          userData = jsonDecode(response.body);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Profil mis à jour avec succès')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour du profil')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur serveur: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> addVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });
    try {
      final data = {
        'driverId': widget.userId,
        'brand': brandController.text.trim(),
        'model': modelController.text.trim(),
        'year': int.parse(yearController.text.trim()),
        'plateNumber': plateNumberController.text.trim(),
        'seats': int.parse(seatsController.text.trim()),
        'allowBaggage': allowBaggageController.text.trim().toLowerCase() == 'true',
      };
      final response = await http.post(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/vehicles'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );
      if (response.statusCode == 201) {
        await fetchProfileData(); // Rafraîchir les données
        brandController.clear();
        modelController.clear();
        yearController.clear();
        plateNumberController.clear();
        seatsController.clear();
        allowBaggageController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Voiture ajoutée avec succès')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'ajout de la voiture')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur serveur: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> updateVehicle(String vehicleId) async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });
    try {
      final data = {
        'brand': brandController.text.trim(),
        'model': modelController.text.trim(),
        'year': int.parse(yearController.text.trim()),
        'plateNumber': plateNumberController.text.trim(),
        'seats': int.parse(seatsController.text.trim()),
        'allowBaggage': allowBaggageController.text.trim().toLowerCase() == 'true',
      };
      final response = await http.put(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/vehicles/$vehicleId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );
      if (response.statusCode == 200) {
        await fetchProfileData(); // Rafraîchir les données
        brandController.clear();
        modelController.clear();
        yearController.clear();
        plateNumberController.clear();
        seatsController.clear();
        allowBaggageController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Voiture mise à jour avec succès')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour de la voiture')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur serveur: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> deleteVehicle(String vehicleId) async {
    setState(() {
      isLoading = true;
    });
    try {
      final response = await http.delete(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/vehicles/$vehicleId'),
        headers: {'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        await fetchProfileData(); // Rafraîchir les données
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Voiture supprimée avec succès')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la suppression de la voiture')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur serveur: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void showVehicleDialog({Map<String, dynamic>? vehicle}) {
    if (vehicle != null) {
      brandController.text = vehicle['brand'] ?? '';
      modelController.text = vehicle['model'] ?? '';
      yearController.text = vehicle['year']?.toString() ?? '';
      plateNumberController.text = vehicle['plateNumber'] ?? '';
      seatsController.text = vehicle['seats']?.toString() ?? '';
      allowBaggageController.text = vehicle['allowBaggage']?.toString() ?? '';
    } else {
      brandController.clear();
      modelController.clear();
      yearController.clear();
      plateNumberController.clear();
      seatsController.clear();
      allowBaggageController.clear();
    }
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(vehicle != null ? 'Modifier la voiture' : 'Ajouter une voiture'),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [
                TextFormField(
                  controller: brandController,
                  decoration: InputDecoration(labelText: 'Marque'),
                  validator: (value) => value!.isEmpty ? 'Marque obligatoire' : null,
                ),
                TextFormField(
                  controller: modelController,
                  decoration: InputDecoration(labelText: 'Modèle'),
                  validator: (value) => value!.isEmpty ? 'Modèle obligatoire' : null,
                ),
                TextFormField(
                  controller: yearController,
                  decoration: InputDecoration(labelText: 'Année'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value!.isEmpty) return 'Année obligatoire';
                    if (int.tryParse(value) == null) return 'Année doit être un nombre';
                    return null;
                  },
                ),
                TextFormField(
                  controller: plateNumberController,
                  decoration: InputDecoration(labelText: 'Numéro d\'immatriculation'),
                  validator: (value) => value!.isEmpty ? 'Numéro obligatoire' : null,
                ),
                TextFormField(
                  controller: seatsController,
                  decoration: InputDecoration(labelText: 'Nombre de sièges'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value!.isEmpty) return 'Nombre de sièges obligatoire';
                    if (int.tryParse(value) == null) return 'Doit être un nombre';
                    return null;
                  },
                ),
                TextFormField(
                  controller: allowBaggageController,
                  decoration: InputDecoration(labelText: 'Bagages autorisés (true/false)'),
                  validator: (value) {
                    if (value!.isEmpty) return 'Bagages obligatoire';
                    if (value.toLowerCase() != 'true' && value.toLowerCase() != 'false') return 'Doit être true ou false';
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                if (vehicle != null) {
                  updateVehicle(vehicle['_id']);
                } else {
                  addVehicle();
                }
                Navigator.pop(context);
              }
            },
            child: Text(vehicle != null ? 'Modifier' : 'Ajouter'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    nomController.dispose();
    prenomController.dispose();
    emailController.dispose();
    phoneController.dispose();
    villeController.dispose();
    numeroPermisController.dispose();
    brandController.dispose();
    modelController.dispose();
    yearController.dispose();
    plateNumberController.dispose();
    seatsController.dispose();
    allowBaggageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Profil du Conducteur')),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Informations du Conducteur', style: Theme.of(context).textTheme.titleLarge),
                  SizedBox(height: 16),
                  Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        TextFormField(
                          controller: nomController,
                          decoration: InputDecoration(labelText: 'Nom'),
                          enabled: isEditing,
                          validator: (value) => value!.isEmpty ? 'Nom obligatoire' : null,
                        ),
                        TextFormField(
                          controller: prenomController,
                          decoration: InputDecoration(labelText: 'Prénom'),
                          enabled: isEditing,
                          validator: (value) => value!.isEmpty ? 'Prénom obligatoire' : null,
                        ),
                        TextFormField(
                          controller: emailController,
                          decoration: InputDecoration(labelText: 'Email'),
                          enabled: isEditing,
                          validator: (value) {
                            if (value!.isEmpty) return 'Email obligatoire';
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Email invalide';
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: phoneController,
                          decoration: InputDecoration(labelText: 'Téléphone'),
                          enabled: isEditing,
                          validator: (value) {
                            if (value!.isEmpty) return 'Téléphone obligatoire';
                            if (!RegExp(r'^\+?[0-9]{10,15}$').hasMatch(value)) {
                              return 'Téléphone invalide';
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: villeController,
                          decoration: InputDecoration(labelText: 'Ville'),
                          enabled: isEditing,
                          validator: (value) => value!.isEmpty ? 'Ville obligatoire' : null,
                        ),
                        TextFormField(
                          controller: numeroPermisController,
                          decoration: InputDecoration(labelText: 'Numéro de permis'),
                          enabled: isEditing,
                          validator: (value) {
                            if (isEditing && value!.isEmpty) return 'Numéro de permis obligatoire pour conducteurs';
                            if (isEditing && !RegExp(r'^[A-Za-z0-9]{6,}$').hasMatch(value!)) {
                              return 'Numéro de permis invalide';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16),
                        Row(
                          children: [
                            ElevatedButton(
                              onPressed: () {
                                if (isEditing) {
                                  updateProfile();
                                } else {
                                  setState(() {
                                    isEditing = true;
                                  });
                                }
                              },
                              child: Text(isEditing ? 'Enregistrer' : 'Modifier'),
                            ),
                            if (isEditing)
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    isEditing = false;
                                    nomController.text = userData?['nom'] ?? '';
                                    prenomController.text = userData?['prenom'] ?? '';
                                    emailController.text = userData?['email'] ?? '';
                                    phoneController.text = userData?['phone'] ?? '';
                                    villeController.text = userData?['ville'] ?? '';
                                    numeroPermisController.text = userData?['numero_permis'] ?? '';
                                  });
                                },
                                child: Text('Annuler'),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 32),
                  Text('Voitures', style: Theme.of(context).textTheme.titleLarge),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => showVehicleDialog(),
                    child: Text('Ajouter une voiture'),
                  ),
                  SizedBox(height: 16),
                  vehicles == null || vehicles!.isEmpty
                      ? Text('Aucune voiture enregistrée')
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: vehicles!.length,
                          itemBuilder: (context, index) {
                            final vehicle = vehicles![index];
                            return Card(
                              child: ListTile(
                                title: Text('${vehicle['brand']} ${vehicle['model']}'),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Année: ${vehicle['year']}'),
                                    Text('Immatriculation: ${vehicle['plateNumber']}'),
                                    Text('Sièges: ${vehicle['seats']}'),
                                    Text('Bagages: ${vehicle['allowBaggage'] ? 'Oui' : 'Non'}'),
                                    Text('Statut: ${vehicle['status']}'),
                                  ],
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(Icons.edit),
                                      onPressed: () => showVehicleDialog(vehicle: vehicle),
                                    ),
                                    IconButton(
                                      icon: Icon(Icons.delete),
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            title: Text('Confirmer la suppression'),
                                            content: Text('Voulez-vous vraiment supprimer cette voiture ?'),
                                            actions: [
                                              TextButton(
                                                onPressed: () => Navigator.pop(context),
                                                child: Text('Annuler'),
                                              ),
                                              ElevatedButton(
                                                onPressed: () {
                                                  deleteVehicle(vehicle['_id']);
                                                  Navigator.pop(context);
                                                },
                                                child: Text('Supprimer'),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ],
              ),
            ),
    );
  }
}