const express = require('express');
const router = express.Router();
const vehicleController = require('../controllers/VehicleController');
const authMiddleware = require('../middlewares/authMiddleware');

router.get('/driver/:driverId', authMiddleware, vehicleController.getVehiclesByDriverId);
router.post('/', authMiddleware, vehicleController.createVehicle);
router.put('/:vehicleId', authMiddleware, vehicleController.updateVehicle);
router.delete('/:vehicleId', authMiddleware, vehicleController.deleteVehicle);

module.exports = router;