const Vehicle = require('../models/Vehicle');

exports.getVehiclesByDriverId = async (req, res) => {
  try {
    const vehicles = await Vehicle.find({ driverId: req.params.driverId });
    res.json(vehicles);
  } catch (error) {
    console.error("Erreur lors de la récupération des véhicules :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.createVehicle = async (req, res) => {
  try {
    const vehicle = new Vehicle(req.body);
    await vehicle.save();
    res.status(201).json(vehicle);
  } catch (error) {
    console.error("Erreur lors de la création du véhicule :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.updateVehicle = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.vehicleId);
    if (!vehicle) return res.status(404).json({ message: 'Voiture non trouvée' });
    if (vehicle.driverId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Non autorisé' });
    }
    const updatedVehicle = await Vehicle.findByIdAndUpdate(
      req.params.vehicleId,
      req.body,
      { new: true, runValidators: true }
    );
    res.json(updatedVehicle);
  } catch (error) {
    console.error("Erreur lors de la mise à jour du véhicule :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.deleteVehicle = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.vehicleId);
    if (!vehicle) return res.status(404).json({ message: 'Voiture non trouvée' });
    if (vehicle.driverId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Non autorisé' });
    }
    await Vehicle.findByIdAndDelete(req.params.vehicleId);
    res.json({ message: 'Voiture supprimée' });
  } catch (error) {
    console.error("Erreur lors de la suppression du véhicule :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};