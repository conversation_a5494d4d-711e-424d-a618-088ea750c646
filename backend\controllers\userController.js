const bcrypt = require('bcryptjs');
const User = require('../models/User');

// Enregistrement initial (sans mot de passe)
const registerUser = async (req, res) => {
  try {
    const { role, nom, prenom, cin, age, email, phone, ville, numero_permis } = req.body;

    console.log("Données reçues :", req.body); // Log pour débogage

    // Vérifier les doublons
    const existingUser = await User.findOne({ $or: [{ cin }, { email }, { phone }] });
    if (existingUser) {
      return res.status(400).json({ message: 'CIN, email ou téléphone déjà utilisé' });
    }

    // Créer l'utilisateur
    const userData = {
      role,
      nom,
      prenom,
      cin,
      age,
      email,
      phone,
      ville,
      isVerified: false
    };

    // Ajouter numero_permis uniquement si le rôle est conducteur
    if (role === 'conducteur') {
      if (!numero_permis) {
        return res.status(400).json({ message: 'Le numéro de permis est requis pour les conducteurs' });
      }
      userData.numero_permis = numero_permis;
    }

    const user = new User(userData);

    await user.save();
    res.status(201).json({ message: 'Utilisateur créé, veuillez définir un mot de passe', userId: user._id });
  } catch (error) {
    console.error("Erreur lors de l'enregistrement :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Définition du mot de passe
const setPassword = async (req, res) => {
  try {
    const { userId, password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      return res.status(400).json({ message: 'Les mots de passe ne correspondent pas' });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    // Hacher le mot de passe
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    user.passwordHash = hashedPassword;
    user.isVerified = true;
    await user.save();

    res.status(200).json({ message: 'Mot de passe défini avec succès' });
  } catch (error) {
    console.error("Erreur lors de la définition du mot de passe :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Connexion de l'utilisateur
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: 'Email ou mot de passe incorrect' });
    }

    if (!user.isVerified) {
      return res.status(400).json({ message: 'Compte non vérifié, veuillez définir un mot de passe' });
    }

    const isMatch = await bcrypt.compare(password, user.passwordHash);
    if (!isMatch) {
      return res.status(400).json({ message: 'Email ou mot de passe incorrect' });
    }

    res.json({ user: { _id: user._id, role: user.role } });
  } catch (error) {
    console.error("Erreur lors de la connexion :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Récupérer les données d'un utilisateur
const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.userId).select('nom prenom email phone ville numero_permis');
    if (!user) return res.status(404).json({ message: 'Utilisateur non trouvé' });
    res.json(user);
  } catch (error) {
    console.error("Erreur lors de la récupération de l'utilisateur :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Mettre à jour les données d'un utilisateur
const updateUser = async (req, res) => {
  try {
    const { nom, prenom, email, phone, ville, numero_permis } = req.body;
    const user = await User.findByIdAndUpdate(
      req.params.userId,
      { nom, prenom, email, phone, ville, numero_permis },
      { new: true, runValidators: true }
    ).select('nom prenom email phone ville numero_permis');
    if (!user) return res.status(404).json({ message: 'Utilisateur non trouvé' });
    res.json(user);
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'utilisateur :", error);
    if (error.code === 11000) {
      return res.status(400).json({ message: 'Email ou téléphone déjà utilisé' });
    }
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

module.exports = { registerUser, setPassword, loginUser, getUserById, updateUser };