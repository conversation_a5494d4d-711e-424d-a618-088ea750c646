const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController'); // Ensure correct path

// Enregistrement d'un utilisateur
router.post('/register', userController.registerUser);

// Définir le mot de passe
router.post('/set-password', userController.setPassword);

// Connexion de l'utilisateur
router.post('/login', userController.loginUser);

// Récupérer les données d'un utilisateur
router.get('/:userId', userController.getUserById);

// Mettre à jour les données d'un utilisateur
router.put('/:userId', userController.updateUser);

module.exports = router;