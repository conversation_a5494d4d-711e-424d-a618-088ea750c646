const mongoose = require('mongoose');

const VehicleSchema = new mongoose.Schema({
  driverId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  brand: { type: String, required: true },
  model: { type: String, required: true },
  year: { type: Number, required: true },
  plateNumber: { type: String, required: true, unique: true },
  seats: { type: Number, required: true },
  allowBaggage: { type: Boolean, default: false },
  status: { type: String, enum: ['active', 'inactive', 'maintenance'], default: 'active' },
  photos: [{ type: String }]
}, { timestamps: true });

module.exports = mongoose.model('Vehicle', VehicleSchema);