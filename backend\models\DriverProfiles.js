const DriverProfileSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', unique: true },
  licenseNumber: { type: String },
  licenseExpiry: { type: Date },
  vehicleIds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Vehicle' }],
  rating: { type: Number, default: 5 },
  documents: [{ type: { type: String }, url: String, status: String }],
  isAvailable: { type: Boolean, default: false },
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], default: [0, 0] } // [lng, lat]
  }
}, { timestamps: true });

DriverProfileSchema.index({ location: '2dsphere' });

module.exports = mongoose.model('DriverProfile', DriverProfileSchema);
