import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'password_page.dart';
import 'conducteur_home_page.dart';

class ConducteurSignupPage extends StatefulWidget {
  const ConducteurSignupPage({super.key});

  @override
  _ConducteurSignupPageState createState() => _ConducteurSignupPageState();
}

class _ConducteurSignupPageState extends State<ConducteurSignupPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nomController = TextEditingController();
  final TextEditingController prenomController = TextEditingController();
  final TextEditingController cinController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController numeroPermisController = TextEditingController();

  bool isLoading = false;

  // Liste complète des 24 gouvernorats et leurs délégations
  final Map<String, List<String>> tunisia = {
    "Ariana": ["Ariana Ville", "Ettadhamen", "Mnihla", "Kalaat El Andalous", "Raoued", "Sidi Thabet", "Soukra"],
    "Béja": ["Béja Nord", "Béja Sud", "Amdoun", "Nefza", "Teboursouk", "Testour", "Goubellat", "Medjez El Bab"],
    "Ben Arous": ["Ben Arous", "Mourouj", "Hammam Lif", "Hammam Chott", "Mornag", "Ezzahra", "Radès", "Mégrine"],
    "Bizerte": ["Bizerte Nord", "Bizerte Sud", "Menzel Bourguiba", "Menzel Jemil", "Utique", "Sejnane", "Joumine", "Ras Jebel"],
    "Gabès": ["Gabès Médina", "Gabès Ouest", "Gabès Sud", "Nouvelle Matmata", "Matmata", "Mareth", "El Hamma"],
    "Gafsa": ["Gafsa Nord", "Gafsa Sud", "Sidi Aïch", "El Ksar", "Mdhilla", "Metlaoui", "Redeyef"],
    "Jendouba": ["Jendouba", "Jendouba Nord", "Aïn Draham", "Fernana", "Ghardimaou", "Tabarka", "Boussalem"],
    "Kairouan": ["Kairouan Nord", "Kairouan Sud", "Chebika", "Sbikha", "Haffouz", "Nasrallah", "El Ala"],
    "Kasserine": ["Kasserine Nord", "Kasserine Sud", "Sbeitla", "Feriana", "Thala", "Haidra", "Foussana"],
    "Kébili": ["Kébili Nord", "Kébili Sud", "Douz Nord", "Douz Sud", "Souk Lahad", "Faouar"],
    "Le Kef": ["Le Kef Est", "Le Kef Ouest", "Nebeur", "Sakiet Sidi Youssef", "Tajerouine", "Dahmani"],
    "Mahdia": ["Mahdia", "Chebba", "Melloulèche", "Ouled Chamekh", "Hebira", "Sidi Alouane", "Ksour Essef"],
    "La Manouba": ["La Manouba", "Den Den", "Douar Hicher", "Oued Ellil", "Tebourba", "Mornaguia", "Borj El Amri"],
    "Médenine": ["Médenine Nord", "Médenine Sud", "Ben Gardane", "Zarzis", "Djerba Houmt Souk", "Djerba Midoun", "Djerba Ajim"],
    "Monastir": ["Monastir", "Ksar Hellal", "Jemmal", "Sayada-Lamta-Bouhjar", "Bembla", "Moknine", "Bekalta"],
    "Nabeul": ["Nabeul", "Hammamet", "Korba", "Dar Chaabane El Fehri", "Beni Khiar", "Menzel Temime", "Kélibia"],
    "Sfax": ["Sfax Ville", "Sfax Ouest", "Sfax Sud", "Sakiet Ezzit", "Sakiet Eddaier", "Thyna", "Menzel Chaker"],
    "Sidi Bouzid": ["Sidi Bouzid Est", "Sidi Bouzid Ouest", "Jelma", "Menzel Bouzaiane", "Mezzouna", "Regueb", "Bir El Hafey"],
    "Siliana": ["Siliana Nord", "Siliana Sud", "Bou Arada", "Gaafour", "Makthar", "Bargou", "Kesra"],
    "Sousse": ["Sousse Médina", "Sousse Riadh", "Sousse Jawhara", "Akouda", "Hammam Sousse", "Enfidha", "Msaken"],
    "Tataouine": ["Tataouine Nord", "Tataouine Sud", "Bir Lahmar", "Ghomrassen", "Smar", "Remada", "Dhehiba"],
    "Tozeur": ["Tozeur", "Degache", "Tameghza", "Nefta", "Hazoua"],
    "Tunis": ["Bab Bhar", "Bab Souika", "El Ouardia", "Le Kram", "La Marsa", "Sidi Hassine", "Carthage", "La Goulette"],
    "Zaghouan": ["Zaghouan", "Zriba", "Bir Mcherga", "El Fahs", "Nadhour", "Saouaf"],
  };

  String? selectedGouvernorat;
  String? selectedDelegation;

  Future<void> registerConducteur() async {
    if (!_formKey.currentState!.validate()) {
      print("Validation du formulaire échouée");
      return;
    }
    if (selectedGouvernorat == null || selectedDelegation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Sélectionnez un gouvernorat et une délégation")),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final Map<String, dynamic> data = {
        "role": "conducteur",
        "nom": nomController.text.trim(),
        "prenom": prenomController.text.trim(),
        "cin": cinController.text.trim(),
        "age": int.parse(ageController.text.trim()),
        "email": emailController.text.trim(),
        "phone": phoneController.text.trim(),
        "ville": "$selectedGouvernorat, $selectedDelegation",
        "numero_permis": numeroPermisController.text.trim(),
      };

      print("Données envoyées : ${jsonEncode(data)}");

      final response = await http.post(
        Uri.parse('https://5778ad53e718.ngrok-free.app/api/register'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(data),
      );

      print("Statut de la réponse : ${response.statusCode}");
      print("Corps de la réponse : ${response.body}");

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        final userId = responseData['userId'].toString();
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => PasswordPage(userId: userId, isConducteur: true)),
        );
        nomController.clear();
        prenomController.clear();
        cinController.clear();
        ageController.clear();
        emailController.clear();
        phoneController.clear();
        numeroPermisController.clear();
        selectedGouvernorat = null;
        selectedDelegation = null;
      } else {
        final errorMessage = jsonDecode(response.body)['message'] ?? "Erreur inconnue";
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: $errorMessage")),
        );
      }
    } catch (e) {
      print("Exception capturée : $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Erreur de connexion au serveur : $e")),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    nomController.dispose();
    prenomController.dispose();
    cinController.dispose();
    ageController.dispose();
    emailController.dispose();
    phoneController.dispose();
    numeroPermisController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Inscription Conducteur")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: nomController,
                decoration: const InputDecoration(labelText: "Nom *"),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Ce champ est obligatoire";
                  }
                  if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) {
                    return "Ce champ doit contenir uniquement des lettres";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: prenomController,
                decoration: const InputDecoration(labelText: "Prénom *"),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Ce champ est obligatoire";
                  }
                  if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) {
                    return "Ce champ doit contenir uniquement des lettres";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: cinController,
                decoration: const InputDecoration(labelText: "CIN (8 chiffres) *"),
                keyboardType: TextInputType.number,
                maxLength: 8,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "CIN est obligatoire";
                  }
                  if (!RegExp(r'^\d{8}$').hasMatch(value)) {
                    return "CIN doit contenir exactement 8 chiffres";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: ageController,
                decoration: const InputDecoration(labelText: "Âge *"),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Âge est obligatoire";
                  }
                  final age = int.tryParse(value);
                  if (age == null) {
                    return "Âge doit être un nombre";
                  }
                  if (age < 18 || age > 70) {
                    return "Âge doit être entre 18 et 70 ans";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: emailController,
                decoration: const InputDecoration(labelText: "Adresse e-mail *"),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Email est obligatoire";
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return "Email invalide";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: phoneController,
                decoration: const InputDecoration(labelText: "Numéro de téléphone *"),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Numéro obligatoire";
                  }
                  if (!RegExp(r'^\d{8}$').hasMatch(value)) {
                    return "Doit contenir exactement 8 chiffres";
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: numeroPermisController,
                decoration: const InputDecoration(labelText: "Numéro de permis *"),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "Numéro de permis est obligatoire";
                  }
                  return null;
                },
              ),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: "Gouvernorat *"),
                value: selectedGouvernorat,
                items: tunisia.keys.map((gouv) => DropdownMenuItem(
                  value: gouv,
                  child: Text(gouv),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedGouvernorat = value;
                    selectedDelegation = null;
                  });
                },
                validator: (value) => value == null ? "Gouvernorat requis" : null,
              ),
              if (selectedGouvernorat != null)
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: "Délégation *"),
                  value: selectedDelegation,
                  items: tunisia[selectedGouvernorat]!
                      .map((deleg) => DropdownMenuItem(
                            value: deleg,
                            child: Text(deleg),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedDelegation = value;
                    });
                  },
                  validator: (value) => value == null ? "Délégation requise" : null,
                ),
              const SizedBox(height: 20),
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: registerConducteur,
                      child: const Text("Suivant"),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}