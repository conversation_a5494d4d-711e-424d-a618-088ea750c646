import 'package:flutter/material.dart';
import 'password_page.dart';
import 'page_home.dart';
import '../services/api_service.dart';

class ClientSignupPage extends StatefulWidget {
  const ClientSignupPage({super.key});

  @override
  _ClientSignupPageState createState() => _ClientSignupPageState();
}

class _ClientSignupPageState extends State<ClientSignupPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nomController = TextEditingController();
  final TextEditingController prenomController = TextEditingController();
  final TextEditingController cinController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  bool isLoading = false;

  // Liste complète des 24 gouvernorats et leurs délégations
  final Map<String, List<String>> tunisia = {
    "Ariana": ["Ariana Ville", "Ettadhamen", "<PERSON><PERSON>hla", "Kalaat El Andalous", "Raoued", "Sidi Thabet", "Soukra"],
    "Béja": ["Béja Nord", "Béja Sud", "Amdoun", "Nefza", "Teboursouk", "Testour", "Goubellat", "Medjez El Bab"],
    "Ben Arous": ["Ben Arous", "Mourouj", "Hammam Lif", "Hammam Chott", "Mornag", "Ezzahra", "Radès", "Mégrine"],
    "Bizerte": ["Bizerte Nord", "Bizerte Sud", "Menzel Bourguiba", "Menzel Jemil", "Utique", "Sejnane", "Joumine", "Ras Jebel"],
    "Gabès": ["Gabès Médina", "Gabès Ouest", "Gabès Sud", "Nouvelle Matmata", "Matmata", "Mareth", "El Hamma"],
    "Gafsa": ["Gafsa Nord", "Gafsa Sud", "Sidi Aïch", "El Ksar", "Mdhilla", "Metlaoui", "Redeyef"],
    "Jendouba": ["Jendouba", "Jendouba Nord", "Aïn Draham", "Fernana", "Ghardimaou", "Tabarka", "Boussalem"],
    "Kairouan": ["Kairouan Nord", "Kairouan Sud", "Chebika", "Sbikha", "Haffouz", "Nasrallah", "El Ala"],
    "Kasserine": ["Kasserine Nord", "Kasserine Sud", "Sbeitla", "Feriana", "Thala", "Haidra", "Foussana"],
    "Kébili": ["Kébili Nord", "Kébili Sud", "Douz Nord", "Douz Sud", "Souk Lahad", "Faouar"],
    "Le Kef": ["Le Kef Est", "Le Kef Ouest", "Nebeur", "Sakiet Sidi Youssef", "Tajerouine", "Dahmani"],
    "Mahdia": ["Mahdia", "Chebba", "Melloulèche", "Ouled Chamekh", "Hebira", "Sidi Alouane", "Ksour Essef"],
    "La Manouba": ["La Manouba", "Den Den", "Douar Hicher", "Oued Ellil", "Tebourba", "Mornaguia", "Borj El Amri"],
    "Médenine": ["Médenine Nord", "Médenine Sud", "Ben Gardane", "Zarzis", "Djerba Houmt Souk", "Djerba Midoun", "Djerba Ajim"],
    "Monastir": ["Monastir", "Ksar Hellal", "Jemmal", "Sayada-Lamta-Bouhjar", "Bembla", "Moknine", "Bekalta"],
    "Nabeul": ["Nabeul", "Hammamet", "Korba", "Dar Chaabane El Fehri", "Beni Khiar", "Menzel Temime", "Kélibia"],
    "Sfax": ["Sfax Ville", "Sfax Ouest", "Sfax Sud", "Sakiet Ezzit", "Sakiet Eddaier", "Thyna", "Menzel Chaker"],
    "Sidi Bouzid": ["Sidi Bouzid Est", "Sidi Bouzid Ouest", "Jelma", "Menzel Bouzaiane", "Mezzouna", "Regueb", "Bir El Hafey"],
    "Siliana": ["Siliana Nord", "Siliana Sud", "Bou Arada", "Gaafour", "Makthar", "Bargou", "Kesra"],
    "Sousse": ["Sousse Médina", "Sousse Riadh", "Sousse Jawhara", "Akouda", "Hammam Sousse", "Enfidha", "Msaken"],
    "Tataouine": ["Tataouine Nord", "Tataouine Sud", "Bir Lahmar", "Ghomrassen", "Smar", "Remada", "Dhehiba"],
    "Tozeur": ["Tozeur", "Degache", "Tameghza", "Nefta", "Hazoua"],
    "Tunis": ["Bab Bhar", "Bab Souika", "El Ouardia", "Le Kram", "La Marsa", "Sidi Hassine", "Carthage", "La Goulette"],
    "Zaghouan": ["Zaghouan", "Zriba", "Bir Mcherga", "El Fahs", "Nadhour", "Saouaf"],
  };

  String? selectedGouvernorat;
  String? selectedDelegation;

  Future<void> registerClient() async {
    if (!_formKey.currentState!.validate()) {
      print("Validation du formulaire échouée");
      return;
    }
    if (selectedGouvernorat == null || selectedDelegation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Sélectionnez un gouvernorat et une délégation")),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final Map<String, dynamic> data = {
        "role": "client",
        "nom": nomController.text.trim(),
        "prenom": prenomController.text.trim(),
        "cin": cinController.text.trim(),
        "age": int.parse(ageController.text.trim()),
        "email": emailController.text.trim(),
        "phone": phoneController.text.trim(),
        "ville": "$selectedGouvernorat, $selectedDelegation",
      };

      // Utiliser le service API pour l'inscription
      final result = await ApiService.register(data);

      if (result.success && mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => PasswordPage(userId: result.userId!)),
        );

        // Nettoyer les champs
        nomController.clear();
        prenomController.clear();
        cinController.clear();
        ageController.clear();
        emailController.clear();
        phoneController.clear();
        setState(() {
          selectedGouvernorat = null;
          selectedDelegation = null;
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.errorMessage ?? "Erreur d'inscription.")),
        );
      }
    } catch (e) {
      print("Exception capturée : $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Erreur de connexion au serveur.")),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    nomController.dispose();
    prenomController.dispose();
    cinController.dispose();
    ageController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Inscription Client")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: nomController,
                decoration: const InputDecoration(labelText: "Nom *"),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "Ce champ est obligatoire";
                  if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) return "Uniquement des lettres";
                  return null;
                },
              ),
              TextFormField(
                controller: prenomController,
                decoration: const InputDecoration(labelText: "Prénom *"),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "Ce champ est obligatoire";
                  if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) return "Uniquement des lettres";
                  return null;
                },
              ),
              TextFormField(
                controller: cinController,
                decoration: const InputDecoration(labelText: "CIN (8 chiffres) *"),
                keyboardType: TextInputType.number,
                maxLength: 8,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "CIN est obligatoire";
                  if (!RegExp(r'^\d{8}$').hasMatch(value)) return "Doit contenir exactement 8 chiffres";
                  return null;
                },
              ),
              TextFormField(
                controller: ageController,
                decoration: const InputDecoration(labelText: "Âge *"),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "Âge est obligatoire";
                  final age = int.tryParse(value);
                  if (age == null) return "Doit être un nombre";
                  if (age < 18 || age > 70) return "Âge entre 18 et 70 ans seulement";
                  return null;
                },
              ),
              TextFormField(
                controller: emailController,
                decoration: const InputDecoration(labelText: "Adresse e-mail *"),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "Email est obligatoire";
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) return "Email invalide";
                  return null;
                },
              ),
              TextFormField(
                controller: phoneController,
                decoration: const InputDecoration(labelText: "Numéro de téléphone *"),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return "Numéro obligatoire";
                  if (!RegExp(r'^\d{8}$').hasMatch(value)) return "Doit contenir exactement 8 chiffres";
                  return null;
                },
              ),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: "Gouvernorat *"),
                value: selectedGouvernorat,
                items: tunisia.keys.map((gouv) => DropdownMenuItem(
                  value: gouv,
                  child: Text(gouv),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedGouvernorat = value;
                    selectedDelegation = null;
                  });
                },
                validator: (value) => value == null ? "Gouvernorat requis" : null,
              ),
              if (selectedGouvernorat != null)
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: "Délégation *"),
                  value: selectedDelegation,
                  items: tunisia[selectedGouvernorat]!
                      .map((deleg) => DropdownMenuItem(
                            value: deleg,
                            child: Text(deleg),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedDelegation = value;
                    });
                  },
                  validator: (value) => value == null ? "Délégation requise" : null,
                ),
              const SizedBox(height: 20),
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: registerClient,
                      child: const Text("Suivant"),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}